name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

jobs:
  laravel-tests:
    name: Tests & Code Quality
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: password
          MYSQL_DATABASE: testing
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.2'
        extensions: mbstring, dom, fileinfo, mysql, xdebug, zip, pcntl, pdo, sqlite, pdo_sqlite
        coverage: xdebug

    - name: Copy .env.ci for testing
      run: cp .env.ci .env

    - name: Cache Composer dependencies
      uses: actions/cache@v3
      with:
        path: vendor
        key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: ${{ runner.os }}-composer-

    - name: Install Composer Dependencies
      run: composer install --no-ansi --no-interaction --prefer-dist

    - name: Generate App Key
      run: php artisan key:generate

    - name: Fix Permissions
      run: chmod -R 777 storage bootstrap/cache

    - name: Create SQLite database
      run: |
        mkdir -p database
        touch database/database.sqlite

    # Nous n'avons plus besoin de Node.js et de la compilation des assets pour les tests
    # car nous avons désactivé le rendu des vues

    - name: Run Pest tests
      run: vendor/bin/pest

    # Désactivé temporairement pour déboguer
    # - name: Upload coverage reports to Codecov
    #   uses: codecov/codecov-action@v3
    #   with:
    #     token: ${{ secrets.CODECOV_TOKEN }}
    #     files: ./coverage.xml
    #     fail_ci_if_error: false

    # Installer PHPStan si nécessaire
    - name: Install PHPStan
      run: composer require --dev phpstan/phpstan || true
      continue-on-error: true

    # Exécuter PHPStan seulement s'il est installé
    - name: Execute PHPStan
      run: |
        if [ -f vendor/bin/phpstan ]; then
          vendor/bin/phpstan analyse --memory-limit=2G || true
        else
          echo "PHPStan not installed, skipping..."
        fi
      continue-on-error: true

    - name: Check code style with Laravel Pint
      run: vendor/bin/pint --test || true
      continue-on-error: true

  # Les jobs de déploiement sont temporairement désactivés jusqu'à ce que les secrets soient configurés
  # Décommentez ces sections une fois que vous avez configuré les secrets nécessaires

  # deploy-staging:
  #   name: Deploy to Staging
  #   needs: laravel-tests
  #   if: (github.ref == 'refs/heads/develop' && github.event_name == 'push') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'staging')
  #   runs-on: ubuntu-latest
  #   environment: staging
  #
  #   steps:
  #   - name: Checkout code
  #     uses: actions/checkout@v3
  #
  #   - name: Setup PHP
  #     uses: shivammathur/setup-php@v2
  #     with:
  #       php-version: '8.2'
  #       extensions: mbstring, dom, fileinfo, mysql, zip
  #
  #   - name: Set up Node.js
  #     uses: actions/setup-node@v3
  #     with:
  #       node-version: '18'
  #       cache: 'npm'
  #
  #   - name: Install Composer Dependencies
  #     run: composer install --no-dev --optimize-autoloader
  #
  #   - name: Install NPM Dependencies
  #     run: npm ci
  #
  #   - name: Build Assets
  #     run: npm run build
  #
  #   - name: Configure SSH
  #     run: |
  #       mkdir -p ~/.ssh/
  #       echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
  #       chmod 600 ~/.ssh/id_rsa
  #       ssh-keyscan -H ${{ secrets.STAGING_HOST }} >> ~/.ssh/known_hosts
  #
  #   - name: Deploy to Staging Server
  #     run: |
  #       rsync -avz --exclude='.git' \
  #                 --exclude='node_modules' \
  #                 --exclude='tests' \
  #                 --exclude='.github' \
  #                 --exclude='.env.example' \
  #                 --exclude='phpunit.xml' \
  #                 --exclude='README.md' \
  #                 ./ ${{ secrets.STAGING_USER }}@${{ secrets.STAGING_HOST }}:${{ secrets.STAGING_PATH }}
  #
  #   - name: Setup Staging Environment
  #     run: |
  #       ssh ${{ secrets.STAGING_USER }}@${{ secrets.STAGING_HOST }} "cd ${{ secrets.STAGING_PATH }} && \
  #       cp .env.staging .env && \
  #       php artisan config:cache && \
  #       php artisan route:cache && \
  #       php artisan view:cache && \
  #       php artisan migrate --force && \
  #       php artisan storage:link && \
  #       chmod -R 775 storage bootstrap/cache"
  #
  #   - name: Notify Slack on Success
  #     uses: rtCamp/action-slack-notify@v2
  #     env:
  #       SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
  #       SLACK_CHANNEL: deployments
  #       SLACK_COLOR: good
  #       SLACK_TITLE: 'Staging Deployment Successful :rocket:'
  #       SLACK_MESSAGE: 'The application has been successfully deployed to staging!'
  #     if: success()
  #
  #   - name: Notify Slack on Failure
  #     uses: rtCamp/action-slack-notify@v2
  #     env:
  #       SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
  #       SLACK_CHANNEL: deployments
  #       SLACK_COLOR: danger
  #       SLACK_TITLE: 'Staging Deployment Failed :x:'
  #       SLACK_MESSAGE: 'The deployment to staging has failed. Please check the GitHub Actions logs.'
  #     if: failure()
  #
  # deploy-production:
  #   name: Deploy to Production
  #   needs: laravel-tests
  #   if: (github.ref == 'refs/heads/main' && github.event_name == 'push') || (github.event_name == 'workflow_dispatch' && github.event.inputs.environment == 'production')
  #   runs-on: ubuntu-latest
  #   environment: production
  #
  #   steps:
  #   - name: Checkout code
  #     uses: actions/checkout@v3
  #
  #   - name: Setup PHP
  #     uses: shivammathur/setup-php@v2
  #     with:
  #       php-version: '8.2'
  #       extensions: mbstring, dom, fileinfo, mysql, zip
  #
  #   - name: Set up Node.js
  #     uses: actions/setup-node@v3
  #     with:
  #       node-version: '18'
  #       cache: 'npm'
  #
  #   - name: Install Composer Dependencies
  #     run: composer install --no-dev --optimize-autoloader
  #
  #   - name: Install NPM Dependencies
  #     run: npm ci
  #
  #   - name: Build Assets
  #     run: npm run build
  #
  #   - name: Configure SSH
  #     run: |
  #       mkdir -p ~/.ssh/
  #       echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
  #       chmod 600 ~/.ssh/id_rsa
  #       ssh-keyscan -H ${{ secrets.PRODUCTION_HOST }} >> ~/.ssh/known_hosts
  #
  #   - name: Deploy to Production Server
  #     run: |
  #       rsync -avz --exclude='.git' \
  #                 --exclude='node_modules' \
  #                 --exclude='tests' \
  #                 --exclude='.github' \
  #                 --exclude='.env.example' \
  #                 --exclude='phpunit.xml' \
  #                 --exclude='README.md' \
  #                 ./ ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }}:${{ secrets.PRODUCTION_PATH }}
  #
  #   - name: Setup Production Environment
  #     run: |
  #       ssh ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} "cd ${{ secrets.PRODUCTION_PATH }} && \
  #       cp .env.production .env && \
  #       php artisan config:cache && \
  #       php artisan route:cache && \
  #       php artisan view:cache && \
  #       php artisan migrate --force && \
  #       php artisan storage:link && \
  #       chmod -R 775 storage bootstrap/cache"
  #
  #   - name: Notify Slack on Success
  #     uses: rtCamp/action-slack-notify@v2
  #     env:
  #       SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
  #       SLACK_CHANNEL: deployments
  #       SLACK_COLOR: good
  #       SLACK_TITLE: 'Production Deployment Successful :rocket:'
  #       SLACK_MESSAGE: 'The application has been successfully deployed to production!'
  #     if: success()
  #
  #   - name: Notify Slack on Failure
  #     uses: rtCamp/action-slack-notify@v2
  #     env:
  #       SLACK_WEBHOOK: ${{ secrets.SLACK_WEBHOOK }}
  #       SLACK_CHANNEL: deployments
  #       SLACK_COLOR: danger
  #       SLACK_TITLE: 'Production Deployment Failed :x:'
  #       SLACK_MESSAGE: 'The deployment to production has failed. Please check the GitHub Actions logs.'
  #     if: failure()
