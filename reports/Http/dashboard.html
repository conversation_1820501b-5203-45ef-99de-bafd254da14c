<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /home/<USER>/roo/M1/fullStack/laravel_project/event-app/app/Http</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=11.0.9" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=11.0.9" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=11.0.9" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/home/<USER>/roo/M1/fullStack/laravel_project/event-app/app</a></li>
         <li class="breadcrumb-item"><a href="index.html">Http</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Controllers/Admin/UserController.php.html#10">App\Http\Controllers\Admin\UserController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/EmailVerificationNotificationController.php.html#9">App\Http\Controllers\Auth\EmailVerificationNotificationController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ClientDashboardController.php.html#8">App\Http\Controllers\ClientDashboardController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PaymentController.php.html#11">App\Http\Controllers\PaymentController</a></td><td class="text-right">34%</td></tr>
       <tr><td><a href="Controllers/EventController.php.html#11">App\Http\Controllers\EventController</a></td><td class="text-right">51%</td></tr>
       <tr><td><a href="Requests/Auth/LoginRequest.php.html#12">App\Http\Requests\Auth\LoginRequest</a></td><td class="text-right">65%</td></tr>
       <tr><td><a href="Controllers/Auth/EmailVerificationPromptController.php.html#10">App\Http\Controllers\Auth\EmailVerificationPromptController</a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="Controllers/Auth/VerifyEmailController.php.html#10">App\Http\Controllers\Auth\VerifyEmailController</a></td><td class="text-right">80%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Controllers/EventController.php.html#11">App\Http\Controllers\EventController</a></td><td class="text-right">140</td></tr>
       <tr><td><a href="Controllers/PaymentController.php.html#11">App\Http\Controllers\PaymentController</a></td><td class="text-right">77</td></tr>
       <tr><td><a href="Controllers/Admin/UserController.php.html#10">App\Http\Controllers\Admin\UserController</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="Requests/Auth/LoginRequest.php.html#12">App\Http\Requests\Auth\LoginRequest</a></td><td class="text-right">9</td></tr>
       <tr><td><a href="Controllers/Auth/EmailVerificationNotificationController.php.html#9">App\Http\Controllers\Auth\EmailVerificationNotificationController</a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/Auth/VerifyEmailController.php.html#10">App\Http\Controllers\Auth\VerifyEmailController</a></td><td class="text-right">3</td></tr>
       <tr><td><a href="Controllers/Auth/EmailVerificationPromptController.php.html#10">App\Http\Controllers\Auth\EmailVerificationPromptController</a></td><td class="text-right">2</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Controllers/Admin/UserController.php.html#15"><abbr title="App\Http\Controllers\Admin\UserController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Admin/UserController.php.html#27"><abbr title="App\Http\Controllers\Admin\UserController::create">create</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Admin/UserController.php.html#36"><abbr title="App\Http\Controllers\Admin\UserController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Admin/UserController.php.html#62"><abbr title="App\Http\Controllers\Admin\UserController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Admin/UserController.php.html#71"><abbr title="App\Http\Controllers\Admin\UserController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Admin/UserController.php.html#81"><abbr title="App\Http\Controllers\Admin\UserController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Admin/UserController.php.html#112"><abbr title="App\Http\Controllers\Admin\UserController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/Auth/EmailVerificationNotificationController.php.html#14"><abbr title="App\Http\Controllers\Auth\EmailVerificationNotificationController::store">store</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/ClientDashboardController.php.html#10"><abbr title="App\Http\Controllers\ClientDashboardController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/EventController.php.html#91"><abbr title="App\Http\Controllers\EventController::show">show</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/EventController.php.html#102"><abbr title="App\Http\Controllers\EventController::edit">edit</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/EventController.php.html#118"><abbr title="App\Http\Controllers\EventController::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/EventController.php.html#163"><abbr title="App\Http\Controllers\EventController::destroy">destroy</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Controllers/PaymentController.php.html#38"><abbr title="App\Http\Controllers\PaymentController::createCheckoutSession">createCheckoutSession</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Requests/Auth/LoginRequest.php.html#60"><abbr title="App\Http\Requests\Auth\LoginRequest::ensureIsNotRateLimited">ensureIsNotRateLimited</abbr></a></td><td class="text-right">20%</td></tr>
       <tr><td><a href="Controllers/EventController.php.html#248"><abbr title="App\Http\Controllers\EventController::publicIndex">publicIndex</abbr></a></td><td class="text-right">60%</td></tr>
       <tr><td><a href="Controllers/Auth/EmailVerificationPromptController.php.html#15"><abbr title="App\Http\Controllers\Auth\EmailVerificationPromptController::__invoke">__invoke</abbr></a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="Controllers/PaymentController.php.html#13"><abbr title="App\Http\Controllers\PaymentController::showCheckoutPage">showCheckoutPage</abbr></a></td><td class="text-right">75%</td></tr>
       <tr><td><a href="Controllers/EventController.php.html#225"><abbr title="App\Http\Controllers\EventController::unregister">unregister</abbr></a></td><td class="text-right">77%</td></tr>
       <tr><td><a href="Controllers/Auth/VerifyEmailController.php.html#15"><abbr title="App\Http\Controllers\Auth\VerifyEmailController::__invoke">__invoke</abbr></a></td><td class="text-right">80%</td></tr>
       <tr><td><a href="Controllers/EventController.php.html#182"><abbr title="App\Http\Controllers\EventController::register">register</abbr></a></td><td class="text-right">85%</td></tr>
       <tr><td><a href="Controllers/EventController.php.html#50"><abbr title="App\Http\Controllers\EventController::store">store</abbr></a></td><td class="text-right">89%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Controllers/EventController.php.html#118"><abbr title="App\Http\Controllers\EventController::update">update</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/PaymentController.php.html#38"><abbr title="App\Http\Controllers\PaymentController::createCheckoutSession">createCheckoutSession</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="Controllers/EventController.php.html#102"><abbr title="App\Http\Controllers\EventController::edit">edit</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/EventController.php.html#163"><abbr title="App\Http\Controllers\EventController::destroy">destroy</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Controllers/PaymentController.php.html#13"><abbr title="App\Http\Controllers\PaymentController::showCheckoutPage">showCheckoutPage</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/EventController.php.html#182"><abbr title="App\Http\Controllers\EventController::register">register</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/Admin/UserController.php.html#81"><abbr title="App\Http\Controllers\Admin\UserController::update">update</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/Auth/EmailVerificationNotificationController.php.html#14"><abbr title="App\Http\Controllers\Auth\EmailVerificationNotificationController::store">store</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Controllers/EventController.php.html#248"><abbr title="App\Http\Controllers\EventController::publicIndex">publicIndex</abbr></a></td><td class="text-right">5</td></tr>
       <tr><td><a href="Requests/Auth/LoginRequest.php.html#60"><abbr title="App\Http\Requests\Auth\LoginRequest::ensureIsNotRateLimited">ensureIsNotRateLimited</abbr></a></td><td class="text-right">4</td></tr>
       <tr><td><a href="Controllers/EventController.php.html#225"><abbr title="App\Http\Controllers\EventController::unregister">unregister</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="Controllers/Auth/VerifyEmailController.php.html#15"><abbr title="App\Http\Controllers\Auth\VerifyEmailController::__invoke">__invoke</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="Controllers/Auth/EmailVerificationPromptController.php.html#15"><abbr title="App\Http\Controllers\Auth\EmailVerificationPromptController::__invoke">__invoke</abbr></a></td><td class="text-right">2</td></tr>
       <tr><td><a href="Controllers/EventController.php.html#50"><abbr title="App\Http\Controllers\EventController::store">store</abbr></a></td><td class="text-right">2</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 11.0.9</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.17</a> and <a href="https://phpunit.de/">PHPUnit 11.5.3</a> at Mon May 19 5:53:37 UTC 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=11.0.9" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=11.0.9" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=11.0.9" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([3,0,0,0,1,0,1,2,0,1,0,11], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([14,0,0,1,0,0,0,2,2,3,0,25], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,8,"<a href=\"Controllers\/Admin\/UserController.php.html#10\">App\\Http\\Controllers\\Admin\\UserController<\/a>"],[100,3,"<a href=\"Controllers\/Auth\/AuthenticatedSessionController.php.html#12\">App\\Http\\Controllers\\Auth\\AuthenticatedSessionController<\/a>"],[100,3,"<a href=\"Controllers\/Auth\/ConfirmablePasswordController.php.html#12\">App\\Http\\Controllers\\Auth\\ConfirmablePasswordController<\/a>"],[0,2,"<a href=\"Controllers\/Auth\/EmailVerificationNotificationController.php.html#9\">App\\Http\\Controllers\\Auth\\EmailVerificationNotificationController<\/a>"],[66.66666666666666,2,"<a href=\"Controllers\/Auth\/EmailVerificationPromptController.php.html#10\">App\\Http\\Controllers\\Auth\\EmailVerificationPromptController<\/a>"],[100,3,"<a href=\"Controllers\/Auth\/NewPasswordController.php.html#16\">App\\Http\\Controllers\\Auth\\NewPasswordController<\/a>"],[100,1,"<a href=\"Controllers\/Auth\/PasswordController.php.html#11\">App\\Http\\Controllers\\Auth\\PasswordController<\/a>"],[100,3,"<a href=\"Controllers\/Auth\/PasswordResetLinkController.php.html#11\">App\\Http\\Controllers\\Auth\\PasswordResetLinkController<\/a>"],[100,2,"<a href=\"Controllers\/Auth\/RegisteredUserController.php.html#15\">App\\Http\\Controllers\\Auth\\RegisteredUserController<\/a>"],[80,3,"<a href=\"Controllers\/Auth\/VerifyEmailController.php.html#10\">App\\Http\\Controllers\\Auth\\VerifyEmailController<\/a>"],[0,1,"<a href=\"Controllers\/ClientDashboardController.php.html#8\">App\\Http\\Controllers\\ClientDashboardController<\/a>"],[100,0,"<a href=\"Controllers\/Controller.php.html#5\">App\\Http\\Controllers\\Controller<\/a>"],[51.45631067961165,31,"<a href=\"Controllers\/EventController.php.html#11\">App\\Http\\Controllers\\EventController<\/a>"],[34.61538461538461,15,"<a href=\"Controllers\/PaymentController.php.html#11\">App\\Http\\Controllers\\PaymentController<\/a>"],[100,4,"<a href=\"Controllers\/ProfileController.php.html#12\">App\\Http\\Controllers\\ProfileController<\/a>"],[100,0,"<a href=\"Kernel.php.html#7\">App\\Http\\Kernel<\/a>"],[100,2,"<a href=\"Middleware\/RoleMiddleware.php.html#9\">App\\Http\\Middleware\\RoleMiddleware<\/a>"],[65.21739130434783,7,"<a href=\"Requests\/Auth\/LoginRequest.php.html#12\">App\\Http\\Requests\\Auth\\LoginRequest<\/a>"],[100,1,"<a href=\"Requests\/ProfileUpdateRequest.php.html#9\">App\\Http\\Requests\\ProfileUpdateRequest<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Controllers\/Admin\/UserController.php.html#15\">App\\Http\\Controllers\\Admin\\UserController::index<\/a>"],[0,1,"<a href=\"Controllers\/Admin\/UserController.php.html#27\">App\\Http\\Controllers\\Admin\\UserController::create<\/a>"],[0,1,"<a href=\"Controllers\/Admin\/UserController.php.html#36\">App\\Http\\Controllers\\Admin\\UserController::store<\/a>"],[0,1,"<a href=\"Controllers\/Admin\/UserController.php.html#62\">App\\Http\\Controllers\\Admin\\UserController::show<\/a>"],[0,1,"<a href=\"Controllers\/Admin\/UserController.php.html#71\">App\\Http\\Controllers\\Admin\\UserController::edit<\/a>"],[0,2,"<a href=\"Controllers\/Admin\/UserController.php.html#81\">App\\Http\\Controllers\\Admin\\UserController::update<\/a>"],[0,1,"<a href=\"Controllers\/Admin\/UserController.php.html#112\">App\\Http\\Controllers\\Admin\\UserController::destroy<\/a>"],[100,1,"<a href=\"Controllers\/Auth\/AuthenticatedSessionController.php.html#17\">App\\Http\\Controllers\\Auth\\AuthenticatedSessionController::create<\/a>"],[100,1,"<a href=\"Controllers\/Auth\/AuthenticatedSessionController.php.html#25\">App\\Http\\Controllers\\Auth\\AuthenticatedSessionController::store<\/a>"],[100,1,"<a href=\"Controllers\/Auth\/AuthenticatedSessionController.php.html#37\">App\\Http\\Controllers\\Auth\\AuthenticatedSessionController::destroy<\/a>"],[100,1,"<a href=\"Controllers\/Auth\/ConfirmablePasswordController.php.html#17\">App\\Http\\Controllers\\Auth\\ConfirmablePasswordController::show<\/a>"],[100,2,"<a href=\"Controllers\/Auth\/ConfirmablePasswordController.php.html#25\">App\\Http\\Controllers\\Auth\\ConfirmablePasswordController::store<\/a>"],[0,2,"<a href=\"Controllers\/Auth\/EmailVerificationNotificationController.php.html#14\">App\\Http\\Controllers\\Auth\\EmailVerificationNotificationController::store<\/a>"],[66.66666666666666,2,"<a href=\"Controllers\/Auth\/EmailVerificationPromptController.php.html#15\">App\\Http\\Controllers\\Auth\\EmailVerificationPromptController::__invoke<\/a>"],[100,1,"<a href=\"Controllers\/Auth\/NewPasswordController.php.html#21\">App\\Http\\Controllers\\Auth\\NewPasswordController::create<\/a>"],[100,2,"<a href=\"Controllers\/Auth\/NewPasswordController.php.html#31\">App\\Http\\Controllers\\Auth\\NewPasswordController::store<\/a>"],[100,1,"<a href=\"Controllers\/Auth\/PasswordController.php.html#16\">App\\Http\\Controllers\\Auth\\PasswordController::update<\/a>"],[100,1,"<a href=\"Controllers\/Auth\/PasswordResetLinkController.php.html#16\">App\\Http\\Controllers\\Auth\\PasswordResetLinkController::create<\/a>"],[100,2,"<a href=\"Controllers\/Auth\/PasswordResetLinkController.php.html#26\">App\\Http\\Controllers\\Auth\\PasswordResetLinkController::store<\/a>"],[100,1,"<a href=\"Controllers\/Auth\/RegisteredUserController.php.html#20\">App\\Http\\Controllers\\Auth\\RegisteredUserController::create<\/a>"],[100,1,"<a href=\"Controllers\/Auth\/RegisteredUserController.php.html#30\">App\\Http\\Controllers\\Auth\\RegisteredUserController::store<\/a>"],[80,3,"<a href=\"Controllers\/Auth\/VerifyEmailController.php.html#15\">App\\Http\\Controllers\\Auth\\VerifyEmailController::__invoke<\/a>"],[0,1,"<a href=\"Controllers\/ClientDashboardController.php.html#10\">App\\Http\\Controllers\\ClientDashboardController::index<\/a>"],[100,2,"<a href=\"Controllers\/EventController.php.html#17\">App\\Http\\Controllers\\EventController::index<\/a>"],[100,1,"<a href=\"Controllers\/EventController.php.html#41\">App\\Http\\Controllers\\EventController::create<\/a>"],[89.47368421052632,2,"<a href=\"Controllers\/EventController.php.html#50\">App\\Http\\Controllers\\EventController::store<\/a>"],[0,1,"<a href=\"Controllers\/EventController.php.html#91\">App\\Http\\Controllers\\EventController::show<\/a>"],[0,3,"<a href=\"Controllers\/EventController.php.html#102\">App\\Http\\Controllers\\EventController::edit<\/a>"],[0,6,"<a href=\"Controllers\/EventController.php.html#118\">App\\Http\\Controllers\\EventController::update<\/a>"],[0,3,"<a href=\"Controllers\/EventController.php.html#163\">App\\Http\\Controllers\\EventController::destroy<\/a>"],[85.71428571428571,6,"<a href=\"Controllers\/EventController.php.html#182\">App\\Http\\Controllers\\EventController::register<\/a>"],[77.77777777777779,3,"<a href=\"Controllers\/EventController.php.html#225\">App\\Http\\Controllers\\EventController::unregister<\/a>"],[60,4,"<a href=\"Controllers\/EventController.php.html#248\">App\\Http\\Controllers\\EventController::publicIndex<\/a>"],[75,6,"<a href=\"Controllers\/PaymentController.php.html#13\">App\\Http\\Controllers\\PaymentController::showCheckoutPage<\/a>"],[0,6,"<a href=\"Controllers\/PaymentController.php.html#38\">App\\Http\\Controllers\\PaymentController::createCheckoutSession<\/a>"],[100,2,"<a href=\"Controllers\/PaymentController.php.html#84\">App\\Http\\Controllers\\PaymentController::success<\/a>"],[100,1,"<a href=\"Controllers\/PaymentController.php.html#97\">App\\Http\\Controllers\\PaymentController::cancel<\/a>"],[100,1,"<a href=\"Controllers\/ProfileController.php.html#17\">App\\Http\\Controllers\\ProfileController::edit<\/a>"],[100,2,"<a href=\"Controllers\/ProfileController.php.html#27\">App\\Http\\Controllers\\ProfileController::update<\/a>"],[100,1,"<a href=\"Controllers\/ProfileController.php.html#43\">App\\Http\\Controllers\\ProfileController::destroy<\/a>"],[100,2,"<a href=\"Middleware\/RoleMiddleware.php.html#16\">App\\Http\\Middleware\\RoleMiddleware::handle<\/a>"],[100,1,"<a href=\"Requests\/Auth\/LoginRequest.php.html#17\">App\\Http\\Requests\\Auth\\LoginRequest::authorize<\/a>"],[100,1,"<a href=\"Requests\/Auth\/LoginRequest.php.html#27\">App\\Http\\Requests\\Auth\\LoginRequest::rules<\/a>"],[100,2,"<a href=\"Requests\/Auth\/LoginRequest.php.html#40\">App\\Http\\Requests\\Auth\\LoginRequest::authenticate<\/a>"],[20,2,"<a href=\"Requests\/Auth\/LoginRequest.php.html#60\">App\\Http\\Requests\\Auth\\LoginRequest::ensureIsNotRateLimited<\/a>"],[100,1,"<a href=\"Requests\/Auth\/LoginRequest.php.html#81\">App\\Http\\Requests\\Auth\\LoginRequest::throttleKey<\/a>"],[100,1,"<a href=\"Requests\/ProfileUpdateRequest.php.html#16\">App\\Http\\Requests\\ProfileUpdateRequest::rules<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>
