<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /home/<USER>/roo/M1/fullStack/laravel_project/event-app/app/Http</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=11.0.9" rel="stylesheet" type="text/css">
  <link href="../_css/octicons.css?v=11.0.9" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=11.0.9" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/home/<USER>/roo/M1/fullStack/laravel_project/event-app/app</a></li>
         <li class="breadcrumb-item active">Http</li>
         <li class="breadcrumb-item">(<a href="dashboard.html">Dashboard</a>)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="9"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="3"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="warning">Total</td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="57.91" aria-valuemin="0" aria-valuemax="100" style="width: 57.91%">
           <span class="visually-hidden">57.91% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">57.91%</div></td>
       <td class="warning small"><div align="right">194&nbsp;/&nbsp;335</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="53.19" aria-valuemin="0" aria-valuemax="100" style="width: 53.19%">
           <span class="visually-hidden">53.19% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">53.19%</div></td>
       <td class="warning small"><div align="right">25&nbsp;/&nbsp;47</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="52.94" aria-valuemin="0" aria-valuemax="100" style="width: 52.94%">
           <span class="visually-hidden">52.94% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">52.94%</div></td>
       <td class="warning small"><div align="right">9&nbsp;/&nbsp;17</div></td>
      </tr>

      <tr>
       <td class="warning"><img src="../_icons/file-directory.svg" class="octicon" /><a href="Controllers/index.html">Controllers</a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="55.37" aria-valuemin="0" aria-valuemax="100" style="width: 55.37%">
           <span class="visually-hidden">55.37% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">55.37%</div></td>
       <td class="warning small"><div align="right">165&nbsp;/&nbsp;298</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="47.50" aria-valuemin="0" aria-valuemax="100" style="width: 47.50%">
           <span class="visually-hidden">47.50% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">47.50%</div></td>
       <td class="danger small"><div align="right">19&nbsp;/&nbsp;40</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="50.00" aria-valuemin="0" aria-valuemax="100" style="width: 50.00%">
           <span class="visually-hidden">50.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">50.00%</div></td>
       <td class="danger small"><div align="right">7&nbsp;/&nbsp;14</div></td>
      </tr>

      <tr>
       <td class="success"><img src="../_icons/file-directory.svg" class="octicon" /><a href="Middleware/index.html">Middleware</a></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">3&nbsp;/&nbsp;3</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
       <td class="success big">       <div class="progress">
         <div class="progress-bar bg-success" role="progressbar" aria-valuenow="100.00" aria-valuemin="0" aria-valuemax="100" style="width: 100.00%">
           <span class="visually-hidden">100.00% covered (success)</span>
         </div>
       </div>
</td>
       <td class="success small"><div align="right">100.00%</div></td>
       <td class="success small"><div align="right">1&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="warning"><img src="../_icons/file-directory.svg" class="octicon" /><a href="Requests/index.html">Requests</a></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="76.47" aria-valuemin="0" aria-valuemax="100" style="width: 76.47%">
           <span class="visually-hidden">76.47% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">76.47%</div></td>
       <td class="warning small"><div align="right">26&nbsp;/&nbsp;34</div></td>
       <td class="warning big">       <div class="progress">
         <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="83.33" aria-valuemin="0" aria-valuemax="100" style="width: 83.33%">
           <span class="visually-hidden">83.33% covered (warning)</span>
         </div>
       </div>
</td>
       <td class="warning small"><div align="right">83.33%</div></td>
       <td class="warning small"><div align="right">5&nbsp;/&nbsp;6</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="50.00" aria-valuemin="0" aria-valuemax="100" style="width: 50.00%">
           <span class="visually-hidden">50.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">50.00%</div></td>
       <td class="danger small"><div align="right">1&nbsp;/&nbsp;2</div></td>
      </tr>

      <tr>
       <td class=""><img src="../_icons/file-code.svg" class="octicon" /><a href="Kernel.php.html">Kernel.php</a></td>
       <td class=" big"></td>
       <td class=" small"><div align="right">n/a</div></td>
       <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
       <td class=" big"></td>
       <td class=" small"><div align="right">n/a</div></td>
       <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
       <td class=" big"></td>
       <td class=" small"><div align="right">n/a</div></td>
       <td class=" small"><div align="right">0&nbsp;/&nbsp;0</div></td>
      </tr>


     </tbody>
    </table>
   </div>
   <footer>
    <hr/>
    <h4>Legend</h4>
    <p>
     <span class="danger"><strong>Low</strong>: 0% to 50%</span>
     <span class="warning"><strong>Medium</strong>: 50% to 90%</span>
     <span class="success"><strong>High</strong>: 90% to 100%</span>
    </p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 11.0.9</a> using <a href="https://www.php.net/" target="_top">PHP 8.3.17</a> and <a href="https://phpunit.de/">PHPUnit 11.5.3</a> at Mon May 19 5:53:37 UTC 2025.</small>
    </p>
   </footer>
  </div>
 </body>
</html>
