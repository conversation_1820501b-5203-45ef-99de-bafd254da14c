<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Tests\TestHelpers\WithoutViews;

abstract class TestCase extends BaseTestCase
{
    use CreatesApplication, WithoutViews;

    protected function setUp(): void
    {
        parent::setUp();

        // Désactiver le rendu des vues pour les tests
        $this->withoutViewRendering();

        // Définir une clé d'application pour les tests
        $this->app['config']->set('app.key', 'base64:'.base64_encode(random_bytes(32)));

        // Désactiver la vérification CSRF pour les tests
        $this->app['config']->set('app.debug', true);
        $this->withoutMiddleware(\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken::class);
    }
}
