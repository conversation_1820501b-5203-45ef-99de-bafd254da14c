<?php

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('email verification prompt is displayed for unverified users', function () {
    $user = User::factory()->unverified()->create();
    
    $response = $this->actingAs($user)->get(route('verification.notice'));
    
    $response->assertStatus(200);
    $response->assertViewIs('auth.verify-email');
});

test('email verification prompt redirects verified users to dashboard', function () {
    $user = User::factory()->create([
        'email_verified_at' => now(),
    ]);
    
    $response = $this->actingAs($user)->get(route('verification.notice'));
    
    $response->assertRedirect(route('dashboard'));
});
