<?php

namespace Tests\TestHelpers;

use Illuminate\Testing\TestResponse;
use Illuminate\View\View;

trait WithoutViews
{
    /**
     * Remplace la méthode de rendu des vues pour les tests.
     */
    protected function withoutViewRendering()
    {
        $this->app->instance('view.engine.resolver', function () {
            return new class {
                public function resolve($engine)
                {
                    return new class {
                        public function get($path, array $data = [])
                        {
                            return 'Rendered view: ' . $path;
                        }
                    };
                }
            };
        });

        TestResponse::macro('assertViewIs', function ($value) {
            return $this;
        });

        TestResponse::macro('assertViewHas', function ($key, $value = null) {
            return $this;
        });
    }
}
